'use client'

import { useEffect, useState, useRef } from 'react'

interface MorphingShapeProps {
  className?: string
  stages: {
    shape: 'circle' | 'rectangle' | 'triangle' | 'pill'
    color: 'red' | 'yellow' | 'blue' | 'black'
    size: number // pixel size
  }[]
  containerSelector?: string // CSS selector for the container to track
}

export default function MorphingShape({
  className = '',
  stages,
  containerSelector = '.how-we-work-section'
}: MorphingShapeProps) {
  const [scrollProgress, setScrollProgress] = useState(0)
  const shapeRef = useRef<HTMLDivElement>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      const container = document.querySelector(containerSelector)
      if (!container) return

      const rect = container.getBoundingClientRect()
      const windowHeight = window.innerHeight

      // More precise calculation - section starts when it enters viewport
      const sectionTop = rect.top + window.scrollY
      const sectionHeight = rect.height

      // Start tracking when section is 20% into viewport
      const trackingStart = sectionTop - windowHeight * 0.8
      const trackingEnd = sectionTop + sectionHeight - windowHeight * 0.2

      const currentScroll = window.scrollY

      if (currentScroll >= trackingStart && currentScroll <= trackingEnd) {
        setIsVisible(true)
        // More precise progress calculation
        const progress = Math.max(0, Math.min(1,
          (currentScroll - trackingStart) / (trackingEnd - trackingStart)
        ))
        setScrollProgress(progress)
      } else {
        setIsVisible(false)
        // Reset to appropriate state when not visible
        if (currentScroll < trackingStart) {
          setScrollProgress(0)
        } else {
          setScrollProgress(1)
        }
      }
    }

    // Initial check
    handleScroll()

    window.addEventListener('scroll', handleScroll, { passive: true })
    window.addEventListener('resize', handleScroll, { passive: true })

    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleScroll)
    }
  }, [containerSelector])

  // Calculate current stage and interpolation with better precision
  const getCurrentStage = () => {
    if (stages.length === 0) return null
    if (stages.length === 1) return { current: stages[0], next: stages[0], blend: 0 }

    // Divide scroll progress into equal segments for each stage
    const segmentSize = 1 / (stages.length - 1)
    const currentSegment = Math.floor(scrollProgress / segmentSize)
    const nextSegment = Math.min(currentSegment + 1, stages.length - 1)

    // Calculate blend within the current segment
    const segmentProgress = (scrollProgress % segmentSize) / segmentSize

    return {
      current: stages[currentSegment],
      next: stages[nextSegment],
      blend: segmentProgress,
      stageIndex: currentSegment
    }
  }

  const stage = getCurrentStage()
  if (!stage) return null

  // Smooth easing function for natural transitions
  const easeInOutCubic = (t: number) => {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2
  }

  // Interpolate size with easing
  const interpolateValue = (current: number, next: number, blend: number) => {
    const easedBlend = easeInOutCubic(blend)
    return current + (next - current) * easedBlend
  }

  const currentSize = interpolateValue(stage.current.size, stage.next.size, stage.blend)

  // Color transition with better timing
  const getCurrentColor = () => {
    const { current, next, blend } = stage

    if (current.color === next.color) return current.color

    // Switch color at 40% through the transition for better visual flow
    return blend < 0.4 ? current.color : next.color
  }

  const currentColor = getCurrentColor()

  // Robust shape morphing with better transitions
  const getShapeStyles = () => {
    const { current, next, blend } = stage
    const size = currentSize

    // If same shape, just return the base style
    if (current.shape === next.shape) {
      return getShapeStyle(current.shape, size, currentColor)
    }

    // Apply easing to blend for smoother transitions
    const easedBlend = easeInOutCubic(blend)

    // Handle specific shape transitions
    if (current.shape === 'circle' && next.shape === 'rectangle') {
      return morphCircleToRectangle(size, easedBlend, currentColor)
    }

    if (current.shape === 'rectangle' && next.shape === 'triangle') {
      return morphRectangleToTriangle(size, easedBlend, currentColor)
    }

    if (current.shape === 'circle' && next.shape === 'triangle') {
      // Direct circle to triangle (skip rectangle)
      return morphCircleToTriangle(size, easedBlend, currentColor)
    }

    // Default: snap transition at 50%
    const useNext = easedBlend > 0.5
    return getShapeStyle(useNext ? next.shape : current.shape, size, currentColor)
  }

  // Individual morph functions for better control
  const morphCircleToRectangle = (size: number, blend: number, color: string) => {
    const borderRadius = Math.max(8, 50 - (blend * 42)) // 50% to 8%
    return {
      width: `${size}px`,
      height: `${size}px`,
      borderRadius: `${borderRadius}%`,
      backgroundColor: getColorValue(color),
      transform: `scale(${1 + blend * 0.05})`, // Subtle scale
      transition: 'none'
    }
  }

  const morphRectangleToTriangle = (size: number, blend: number, color: string) => {
    if (blend < 0.4) {
      // Phase 1: Rectangle starts to taper at top
      const topTaper = blend / 0.4 * 20 // 0% to 20% taper
      const clipPath = `polygon(${topTaper}% 0%, ${100-topTaper}% 0%, 100% 100%, 0% 100%)`
      return {
        width: `${size}px`,
        height: `${size}px`,
        backgroundColor: getColorValue(color),
        clipPath,
        borderRadius: `${8 - blend * 8}%`, // Reduce border radius
        transition: 'none'
      }
    } else {
      // Phase 2: Complete triangle formation
      const triangleProgress = (blend - 0.4) / 0.6
      const clipPath = `polygon(50% 0%, 100% 100%, 0% 100%)`
      return {
        width: `${size}px`,
        height: `${size}px`,
        backgroundColor: getColorValue(color),
        clipPath,
        transform: `scale(${1 + triangleProgress * 0.1})`,
        transition: 'none'
      }
    }
  }

  const morphCircleToTriangle = (size: number, blend: number, color: string) => {
    if (blend < 0.5) {
      // First half: circle to diamond-ish
      const borderRadius = 50 - (blend * 2 * 30) // 50% to 20%
      return {
        width: `${size}px`,
        height: `${size}px`,
        borderRadius: `${borderRadius}%`,
        backgroundColor: getColorValue(color),
        transform: `rotate(${blend * 45}deg)`, // Slight rotation
        transition: 'none'
      }
    } else {
      // Second half: diamond to triangle
      const triangleProgress = (blend - 0.5) * 2
      const clipPath = `polygon(50% 0%, 100% 100%, 0% 100%)`
      return {
        width: `${size}px`,
        height: `${size}px`,
        backgroundColor: getColorValue(color),
        clipPath,
        transform: `rotate(0deg) scale(${1 + triangleProgress * 0.1})`,
        transition: 'none'
      }
    }
  }

  const getShapeStyle = (shape: string, size: string, color: string) => {
    const baseStyle = {
      width: size,
      height: size,
      backgroundColor: getColorValue(color),
      transition: 'none'
    }

    switch (shape) {
      case 'circle':
        return { ...baseStyle, borderRadius: '50%' }
      case 'rectangle':
        return { ...baseStyle, borderRadius: '8px' }
      case 'triangle':
        return { 
          ...baseStyle, 
          clipPath: 'polygon(50% 0%, 100% 100%, 0% 100%)',
          backgroundColor: getColorValue(color)
        }
      case 'pill':
        return { 
          ...baseStyle, 
          width: `${parseInt(size) * 1.5}px`,
          borderRadius: '50px' 
        }
      default:
        return baseStyle
    }
  }

  const getColorValue = (color: string) => {
    const colors = {
      red: '#e94436',
      yellow: '#ffc527',
      blue: '#434897',
      black: '#000000'
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  // Only render when visible in the section
  if (!isVisible) return null

  return (
    <div className="fixed top-0 left-0 w-full h-full pointer-events-none z-10">
      <div
        ref={shapeRef}
        className={`absolute ${className}`}
        style={{
          left: '50%',
          top: '50%',
          transform: 'translate(-50%, -50%)',
          ...getShapeStyles()
        }}
      />
    </div>
  )
}
