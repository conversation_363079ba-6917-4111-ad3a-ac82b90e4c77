'use client'

import { useEffect, useState, useRef } from 'react'

interface MorphingShapeProps {
  className?: string
  stages: {
    shape: 'circle' | 'rectangle' | 'triangle' | 'pill'
    color: 'red' | 'yellow' | 'blue' | 'black'
    size: number // 0-100 scale
    position: { x: number; y: number } // 0-100 percentage
  }[]
}

export default function MorphingShape({ className = '', stages }: MorphingShapeProps) {
  const [scrollProgress, setScrollProgress] = useState(0)
  const sectionRef = useRef<HTMLDivElement>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return

      const rect = sectionRef.current.getBoundingClientRect()
      const sectionTop = rect.top
      const sectionHeight = rect.height
      const windowHeight = window.innerHeight

      // Calculate when section enters and exits viewport
      const sectionStart = sectionTop + window.scrollY - windowHeight
      const sectionEnd = sectionTop + window.scrollY + sectionHeight

      const currentScroll = window.scrollY
      
      // Check if section is in viewport
      if (currentScroll >= sectionStart && currentScroll <= sectionEnd) {
        setIsVisible(true)
        // Calculate progress through the section (0 to 1)
        const progress = Math.max(0, Math.min(1, 
          (currentScroll - sectionStart) / (sectionEnd - sectionStart)
        ))
        setScrollProgress(progress)
      } else {
        setIsVisible(false)
      }
    }

    // Initial check
    handleScroll()
    
    window.addEventListener('scroll', handleScroll, { passive: true })
    window.addEventListener('resize', handleScroll, { passive: true })

    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleScroll)
    }
  }, [])

  // Calculate current stage and interpolation
  const getCurrentStage = () => {
    if (stages.length === 0) return null
    if (stages.length === 1) return { current: stages[0], next: stages[0], blend: 0 }

    const stageProgress = scrollProgress * (stages.length - 1)
    const currentIndex = Math.floor(stageProgress)
    const nextIndex = Math.min(currentIndex + 1, stages.length - 1)
    const blend = stageProgress - currentIndex

    return {
      current: stages[currentIndex],
      next: stages[nextIndex],
      blend: blend
    }
  }

  const stage = getCurrentStage()
  if (!stage || !isVisible) return <div ref={sectionRef} className="absolute inset-0 pointer-events-none" />

  // Interpolate between current and next stage
  const interpolateValue = (current: number, next: number, blend: number) => {
    return current + (next - current) * blend
  }

  const currentSize = interpolateValue(stage.current.size, stage.next.size, stage.blend)
  const currentX = interpolateValue(stage.current.position.x, stage.next.position.x, stage.blend)
  const currentY = interpolateValue(stage.current.position.y, stage.next.position.y, stage.blend)

  // Smooth color interpolation
  const getCurrentColor = () => {
    const { current, next, blend } = stage

    if (current.color === next.color) return current.color

    // For smooth color transitions, we'll use CSS custom properties
    // But for now, let's do a smoother switch point
    const colorSwitchPoint = 0.3 // Switch earlier for better visual flow
    return blend < colorSwitchPoint ? current.color : next.color
  }

  const currentColor = getCurrentColor()

  // Shape morphing using CSS clip-path and border-radius
  const getShapeStyles = () => {
    const { current, next, blend } = stage

    // Base size calculation
    const size = `${currentSize}px`

    if (current.shape === next.shape) {
      // Same shape, just animate normally
      return getShapeStyle(current.shape, size, currentColor)
    }

    // Smooth easing for morphing
    const easeInOutCubic = (t: number) => {
      return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2
    }
    const easedBlend = easeInOutCubic(blend)

    // Different shapes - morph between them
    if (current.shape === 'circle' && next.shape === 'rectangle') {
      const borderRadius = `${50 - (easedBlend * 42)}%` // 50% to 8%
      return {
        width: size,
        height: size,
        borderRadius,
        backgroundColor: getColorValue(currentColor),
        transition: 'none',
        transform: `scale(${1 + easedBlend * 0.1})` // Slight scale during morph
      }
    }

    if (current.shape === 'rectangle' && next.shape === 'triangle') {
      // Smooth rectangle to triangle transition
      if (easedBlend < 0.3) {
        // Start as rectangle, begin to taper top
        const topWidth = 100 - (easedBlend / 0.3) * 30 // 100% to 70%
        const clipPath = `polygon(${(100-topWidth)/2}% 0%, ${50+topWidth/2}% 0%, 100% 100%, 0% 100%)`
        return {
          width: size,
          height: size,
          backgroundColor: getColorValue(currentColor),
          clipPath,
          transition: 'none'
        }
      } else {
        // Complete triangle formation
        const progress = (easedBlend - 0.3) / 0.7
        const clipPath = `polygon(50% 0%, 100% 100%, 0% 100%)`
        return {
          width: size,
          height: size,
          backgroundColor: getColorValue(currentColor),
          clipPath,
          transition: 'none',
          transform: `scale(${1 + progress * 0.1})` // Slight growth as triangle forms
        }
      }
    }

    if (current.shape === 'circle' && next.shape === 'pill') {
      const widthMultiplier = 1 + (easedBlend * 0.8) // Stretch to 1.8x width
      return {
        width: `${currentSize * widthMultiplier}px`,
        height: size,
        borderRadius: `${50 - (easedBlend * 25)}px`, // Maintain rounded ends
        backgroundColor: getColorValue(currentColor),
        transition: 'none'
      }
    }

    // Default fallback with smooth transition
    return getShapeStyle(easedBlend < 0.5 ? current.shape : next.shape, size, currentColor)
  }

  const getShapeStyle = (shape: string, size: string, color: string) => {
    const baseStyle = {
      width: size,
      height: size,
      backgroundColor: getColorValue(color),
      transition: 'none'
    }

    switch (shape) {
      case 'circle':
        return { ...baseStyle, borderRadius: '50%' }
      case 'rectangle':
        return { ...baseStyle, borderRadius: '8px' }
      case 'triangle':
        return { 
          ...baseStyle, 
          clipPath: 'polygon(50% 0%, 100% 100%, 0% 100%)',
          backgroundColor: getColorValue(color)
        }
      case 'pill':
        return { 
          ...baseStyle, 
          width: `${parseInt(size) * 1.5}px`,
          borderRadius: '50px' 
        }
      default:
        return baseStyle
    }
  }

  const getColorValue = (color: string) => {
    const colors = {
      red: '#e94436',
      yellow: '#ffc527',
      blue: '#434897',
      black: '#000000'
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  return (
    <div ref={sectionRef} className="absolute inset-0 pointer-events-none">
      <div
        className={`absolute transition-all duration-100 ease-out ${className}`}
        style={{
          left: `${currentX}%`,
          top: `${currentY}%`,
          transform: 'translate(-50%, -50%)',
          ...getShapeStyles()
        }}
      />
    </div>
  )
}
